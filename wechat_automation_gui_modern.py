#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信自动化添加好友 - 现代化科技感图形用户界面
功能：为main_controller.py提供可视化操作界面

核心特性：
1. 实时监控执行状态和进度
2. 可视化配置管理
3. 日志实时显示
4. 统计信息展示
5. 一键启动/停止控制
6. 多窗口状态监控
7. 现代化深色主题设计
8. 科技感动画效果
9. 高级视觉反馈

版本：2.0.0 - 现代化科技感版本
作者：AI助手
创建时间：2025-01-28
更新时间：2025-01-31
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import threading
import queue
import json
import time
from datetime import datetime
from pathlib import Path
import logging
from typing import Dict, List, Optional
import math

# 导入主控制器
try:
    from main_controller import WeChatMainController, ExecutionStep, WindowStatus
except ImportError:
    # 如果主控制器不存在，创建模拟类
    class WeChatMainController:
        def __init__(self, *args, **kwargs):
            pass
    
    class ExecutionStep:
        pass
    
    class WindowStatus:
        pass

# 现代化科技感配色方案
class ModernColors:
    """现代化科技感配色方案"""
    # 深色主题基础色
    DARK_BG = "#1a1a1a"           # 主背景色
    DARKER_BG = "#0f0f0f"         # 更深背景色
    CARD_BG = "#2d2d2d"           # 卡片背景色
    HOVER_BG = "#3d3d3d"          # 悬停背景色
    
    # 科技蓝色系
    PRIMARY_BLUE = "#00d4ff"      # 主要蓝色
    SECONDARY_BLUE = "#0099cc"    # 次要蓝色
    ACCENT_BLUE = "#66e0ff"       # 强调蓝色
    GLOW_BLUE = "#00aaff"         # 发光蓝色
    
    # 状态色彩
    SUCCESS_GREEN = "#00ff88"     # 成功绿色
    WARNING_ORANGE = "#ff9900"    # 警告橙色
    ERROR_RED = "#ff4444"         # 错误红色
    INFO_CYAN = "#00ffff"         # 信息青色
    
    # 文本色彩
    TEXT_PRIMARY = "#ffffff"      # 主要文本
    TEXT_SECONDARY = "#cccccc"    # 次要文本
    TEXT_MUTED = "#888888"        # 弱化文本
    TEXT_ACCENT = "#00d4ff"       # 强调文本
    
    # 边框和分隔线
    BORDER_LIGHT = "#444444"      # 浅色边框
    BORDER_DARK = "#222222"       # 深色边框
    SEPARATOR = "#333333"         # 分隔线
    
    # 渐变色
    GRADIENT_START = "#1a1a1a"    # 渐变起始
    GRADIENT_END = "#2d2d2d"      # 渐变结束

class ModernFonts:
    """现代化字体配置"""
    # 字体族
    PRIMARY_FONT = "Segoe UI"     # 主要字体
    MONO_FONT = "Consolas"        # 等宽字体
    ICON_FONT = "Segoe UI Symbol" # 图标字体
    
    # 字体大小
    TITLE_SIZE = 16               # 标题字体
    LARGE_SIZE = 14               # 大字体
    NORMAL_SIZE = 12              # 正常字体
    SMALL_SIZE = 10               # 小字体
    TINY_SIZE = 9                 # 微小字体

class ModernProgressBar:
    """现代化进度条组件"""
    
    def __init__(self, parent, width=300, height=20):
        self.parent = parent
        self.width = width
        self.height = height
        self.progress = 0
        self.max_value = 100
        
        # 创建画布
        self.canvas = tk.Canvas(parent, width=width, height=height,
                               bg=ModernColors.CARD_BG, highlightthickness=0)
        self.canvas.pack()
        
        # 绘制初始进度条
        self.draw_progress()
    
    def draw_progress(self):
        """绘制进度条"""
        self.canvas.delete("all")
        
        # 背景
        self.canvas.create_rectangle(0, 0, self.width, self.height,
                                   fill=ModernColors.DARKER_BG, outline="")
        
        # 进度条
        if self.progress > 0:
            progress_width = (self.progress / self.max_value) * self.width
            
            # 渐变效果
            for i in range(int(progress_width)):
                ratio = i / self.width
                # 从主蓝色到强调蓝色的渐变
                r1, g1, b1 = 0, 212, 255    # PRIMARY_BLUE
                r2, g2, b2 = 102, 224, 255  # ACCENT_BLUE
                
                r = int(r1 + (r2 - r1) * ratio)
                g = int(g1 + (g2 - g1) * ratio)
                b = int(b1 + (b2 - b1) * ratio)
                
                color = f"#{r:02x}{g:02x}{b:02x}"
                self.canvas.create_line(i, 0, i, self.height, fill=color)
        
        # 边框
        self.canvas.create_rectangle(0, 0, self.width, self.height,
                                   fill="", outline=ModernColors.BORDER_LIGHT)
    
    def set_progress(self, value):
        """设置进度值"""
        self.progress = max(0, min(self.max_value, value))
        self.draw_progress()
    
    def set_max(self, max_value):
        """设置最大值"""
        self.max_value = max_value
        self.draw_progress()

class ModernButton:
    """现代化按钮组件"""
    
    def __init__(self, parent, text="", command=None, style="primary"):
        self.parent = parent
        self.text = text
        self.command = command
        self.style = style
        self.is_hovered = False
        self.is_pressed = False
        
        # 样式配置
        self.styles = {
            "primary": {
                "bg": ModernColors.PRIMARY_BLUE,
                "fg": ModernColors.TEXT_PRIMARY,
                "hover_bg": ModernColors.ACCENT_BLUE,
                "press_bg": ModernColors.SECONDARY_BLUE
            },
            "secondary": {
                "bg": ModernColors.CARD_BG,
                "fg": ModernColors.TEXT_PRIMARY,
                "hover_bg": ModernColors.HOVER_BG,
                "press_bg": ModernColors.BORDER_LIGHT
            },
            "success": {
                "bg": ModernColors.SUCCESS_GREEN,
                "fg": ModernColors.DARK_BG,
                "hover_bg": "#33ff99",
                "press_bg": "#00cc66"
            },
            "danger": {
                "bg": ModernColors.ERROR_RED,
                "fg": ModernColors.TEXT_PRIMARY,
                "hover_bg": "#ff6666",
                "press_bg": "#cc3333"
            }
        }
        
        # 创建按钮
        self.create_button()
    
    def create_button(self):
        """创建按钮"""
        style_config = self.styles.get(self.style, self.styles["primary"])
        
        self.button = tk.Button(
            self.parent,
            text=self.text,
            command=self.command,
            bg=style_config["bg"],
            fg=style_config["fg"],
            font=(ModernFonts.PRIMARY_FONT, ModernFonts.NORMAL_SIZE, "bold"),
            relief="flat",
            borderwidth=0,
            padx=20,
            pady=10,
            cursor="hand2"
        )
        
        # 绑定事件
        self.button.bind("<Enter>", self.on_enter)
        self.button.bind("<Leave>", self.on_leave)
        self.button.bind("<Button-1>", self.on_press)
        self.button.bind("<ButtonRelease-1>", self.on_release)
    
    def on_enter(self, event):
        """鼠标进入事件"""
        self.is_hovered = True
        style_config = self.styles.get(self.style, self.styles["primary"])
        self.button.config(bg=style_config["hover_bg"])
    
    def on_leave(self, event):
        """鼠标离开事件"""
        self.is_hovered = False
        style_config = self.styles.get(self.style, self.styles["primary"])
        self.button.config(bg=style_config["bg"])
    
    def on_press(self, event):
        """鼠标按下事件"""
        self.is_pressed = True
        style_config = self.styles.get(self.style, self.styles["primary"])
        self.button.config(bg=style_config["press_bg"])
    
    def on_release(self, event):
        """鼠标释放事件"""
        self.is_pressed = False
        style_config = self.styles.get(self.style, self.styles["primary"])
        if self.is_hovered:
            self.button.config(bg=style_config["hover_bg"])
        else:
            self.button.config(bg=style_config["bg"])
    
    def pack(self, **kwargs):
        """打包按钮"""
        self.button.pack(**kwargs)
    
    def grid(self, **kwargs):
        """网格布局按钮"""
        self.button.grid(**kwargs)
    
    def config(self, **kwargs):
        """配置按钮"""
        self.button.config(**kwargs)

class ModernCard:
    """现代化卡片组件"""
    
    def __init__(self, parent, title="", padding=20):
        self.parent = parent
        self.title = title
        self.padding = padding
        
        # 创建卡片框架
        self.frame = tk.Frame(parent, bg=ModernColors.CARD_BG, relief="flat", bd=1)
        
        # 添加阴影效果
        self.shadow_frame = tk.Frame(parent, bg=ModernColors.BORDER_DARK, height=2)
        
        # 标题区域
        if title:
            self.title_frame = tk.Frame(self.frame, bg=ModernColors.CARD_BG)
            self.title_frame.pack(fill=tk.X, padx=padding, pady=(padding, 0))
            
            self.title_label = tk.Label(
                self.title_frame,
                text=title,
                bg=ModernColors.CARD_BG,
                fg=ModernColors.TEXT_ACCENT,
                font=(ModernFonts.PRIMARY_FONT, ModernFonts.LARGE_SIZE, "bold")
            )
            self.title_label.pack(anchor=tk.W)
            
            # 分隔线
            separator = tk.Frame(self.frame, bg=ModernColors.BORDER_LIGHT, height=1)
            separator.pack(fill=tk.X, padx=padding, pady=(10, 0))
        
        # 内容区域
        self.content_frame = tk.Frame(self.frame, bg=ModernColors.CARD_BG)
        self.content_frame.pack(fill=tk.BOTH, expand=True, padx=padding, pady=padding)
    
    def pack(self, **kwargs):
        """打包卡片"""
        # 处理pady参数
        pady = kwargs.get('pady', 0)
        if isinstance(pady, tuple):
            shadow_pady = (pady[0] + 2, 0)
        else:
            shadow_pady = (pady + 2, 0)

        self.shadow_frame.pack(fill=tk.X, pady=shadow_pady)
        self.frame.pack(**kwargs)
    
    def get_content_frame(self):
        """获取内容框架"""
        return self.content_frame

class WeChatAutomationModernGUI:
    """微信自动化添加好友现代化科技感图形用户界面"""
    
    def __init__(self):
        """初始化现代化GUI"""
        self.root = tk.Tk()
        self.root.title("🚀 微信自动化控制台 v2.0.0 - 科技感版本")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 700)

        # 控制器和状态
        self.controller: Optional[WeChatMainController] = None
        self.is_running = False
        self.automation_thread: Optional[threading.Thread] = None

        # 消息队列用于线程间通信
        self.message_queue = queue.Queue()

        # 动画相关变量 - 必须在setup_modern_theme之前定义
        self.animation_running = False
        self.glow_intensity = 0.3
        self.pulse_direction = 1

        # 设置现代化深色主题
        self.setup_modern_theme()

        # 设置现代化样式
        self.setup_modern_styles()
        
        # 状态数据
        self.execution_stats = {
            "total_contacts": 0,
            "processed_contacts": 0,
            "successful_adds": 0,
            "failed_adds": 0,
            "skipped_contacts": 0,
            "total_windows": 0,
            "completed_windows": 0,
            "start_time": None,
            "end_time": None
        }

        # 运行时参数变量
        self.runtime_params = {
            "interval_min": tk.StringVar(value="50"),
            "interval_max": tk.StringVar(value="60"),
            "daily_limit": tk.StringVar(value="200"),
            "max_per_window": tk.StringVar(value="20"),
            "morning_start": tk.StringVar(value="10:00"),
            "morning_end": tk.StringVar(value="12:00"),
            "afternoon_start": tk.StringVar(value="14:00"),
            "afternoon_end": tk.StringVar(value="23:59"),
            "rest_trigger": tk.StringVar(value="20"),
            "rest_duration": tk.StringVar(value="5")
        }

        # 时段启用/禁用状态变量
        self.time_slot_enabled = {
            "morning_enabled": tk.BooleanVar(value=True),
            "afternoon_enabled": tk.BooleanVar(value=True)
        }

        # 运行状态变量
        self.status_vars = {
            "total_progress": tk.StringVar(value="0/0 (0%)"),
            "planned_count": tk.StringVar(value="0"),
            "current_progress": tk.StringVar(value="0"),
            "success_count": tk.StringVar(value="0"),
            "error_count": tk.StringVar(value="0"),
            "current_window": tk.StringVar(value="0/0"),
            "countdown": tk.StringVar(value="0")
        }

        # 创建界面
        self.create_widgets()
        self.setup_logging()

        # 启动消息处理
        self.process_messages()

        # 加载配置
        self.load_configuration()
        self.load_runtime_params_from_config()

    def setup_modern_theme(self):
        """设置现代化深色主题"""
        # 配置根窗口
        self.root.configure(bg=ModernColors.DARK_BG)

        # 设置窗口图标和属性
        try:
            # 尝试设置窗口图标（如果有的话）
            self.root.iconbitmap(default="icon.ico")
        except:
            pass

        # 设置窗口透明度和特效
        self.root.attributes('-alpha', 0.98)  # 轻微透明效果

        # 启动动画效果
        self.start_animations()

    def setup_modern_styles(self):
        """设置现代化样式"""
        style = ttk.Style()

        # 使用深色主题
        style.theme_use('clam')

        # 配置现代化标签样式
        style.configure('Modern.Title.TLabel',
                       font=(ModernFonts.PRIMARY_FONT, ModernFonts.TITLE_SIZE, 'bold'),
                       foreground=ModernColors.TEXT_PRIMARY,
                       background=ModernColors.DARK_BG)

        style.configure('Modern.Subtitle.TLabel',
                       font=(ModernFonts.PRIMARY_FONT, ModernFonts.LARGE_SIZE),
                       foreground=ModernColors.TEXT_SECONDARY,
                       background=ModernColors.DARK_BG)

        style.configure('Modern.Status.TLabel',
                       font=(ModernFonts.PRIMARY_FONT, ModernFonts.NORMAL_SIZE),
                       foreground=ModernColors.TEXT_ACCENT,
                       background=ModernColors.DARK_BG)

        style.configure('Modern.Success.TLabel',
                       font=(ModernFonts.PRIMARY_FONT, ModernFonts.NORMAL_SIZE, 'bold'),
                       foreground=ModernColors.SUCCESS_GREEN,
                       background=ModernColors.DARK_BG)

        style.configure('Modern.Warning.TLabel',
                       font=(ModernFonts.PRIMARY_FONT, ModernFonts.NORMAL_SIZE, 'bold'),
                       foreground=ModernColors.WARNING_ORANGE,
                       background=ModernColors.DARK_BG)

        style.configure('Modern.Error.TLabel',
                       font=(ModernFonts.PRIMARY_FONT, ModernFonts.NORMAL_SIZE, 'bold'),
                       foreground=ModernColors.ERROR_RED,
                       background=ModernColors.DARK_BG)

    def start_animations(self):
        """启动动画效果"""
        self.animation_running = True
        self.animate_glow()

    def animate_glow(self):
        """发光动画效果"""
        if not self.animation_running:
            return

        # 更新发光强度
        self.glow_intensity += self.pulse_direction * 0.02
        if self.glow_intensity >= 1.0:
            self.glow_intensity = 1.0
            self.pulse_direction = -1
        elif self.glow_intensity <= 0.3:
            self.glow_intensity = 0.3
            self.pulse_direction = 1

        # 继续动画
        self.root.after(50, self.animate_glow)

    def create_widgets(self):
        """创建现代化界面组件"""
        # 创建主框架
        self.create_main_frame()

        # 创建菜单栏
        self.create_menu()

        # 创建工具栏
        self.create_toolbar()

        # 创建主要内容区域
        self.create_content_area()

        # 创建状态栏
        self.create_status_bar()

    def create_main_frame(self):
        """创建现代化主框架"""
        # 主容器 - 使用现代化样式
        self.main_frame = tk.Frame(self.root, bg=ModernColors.DARK_BG)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=8, pady=8)

        # 添加渐变背景效果
        self.create_gradient_background()

    def create_gradient_background(self):
        """创建渐变背景效果"""
        # 创建画布用于渐变背景
        self.bg_canvas = tk.Canvas(self.main_frame,
                                  bg=ModernColors.DARK_BG,
                                  highlightthickness=0)
        self.bg_canvas.place(x=0, y=0, relwidth=1, relheight=1)

        # 绘制渐变背景
        self.draw_gradient_background()

    def draw_gradient_background(self):
        """绘制渐变背景"""
        try:
            width = self.bg_canvas.winfo_width()
            height = self.bg_canvas.winfo_height()

            if width > 1 and height > 1:
                self.bg_canvas.delete("gradient")

                # 创建垂直渐变
                for i in range(height):
                    ratio = i / height
                    # 从深色到稍浅的深色渐变
                    r1, g1, b1 = 26, 26, 26    # DARK_BG
                    r2, g2, b2 = 45, 45, 45    # 稍浅的深色

                    r = int(r1 + (r2 - r1) * ratio)
                    g = int(g1 + (g2 - g1) * ratio)
                    b = int(b1 + (b2 - b1) * ratio)

                    color = f"#{r:02x}{g:02x}{b:02x}"
                    self.bg_canvas.create_line(0, i, width, i, fill=color, tags="gradient")
        except:
            pass

        # 定期更新渐变背景
        self.root.after(100, self.draw_gradient_background)

    def create_menu(self):
        """创建现代化菜单栏"""
        menubar = tk.Menu(self.root, bg=ModernColors.CARD_BG, fg=ModernColors.TEXT_PRIMARY)
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0, bg=ModernColors.CARD_BG, fg=ModernColors.TEXT_PRIMARY)
        menubar.add_cascade(label="📁 文件", menu=file_menu)
        file_menu.add_command(label="🔄 重新加载配置", command=self.reload_config)
        file_menu.add_command(label="💾 保存配置", command=self.save_configuration)
        file_menu.add_separator()
        file_menu.add_command(label="🚪 退出", command=self.on_closing)

        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0, bg=ModernColors.CARD_BG, fg=ModernColors.TEXT_PRIMARY)
        menubar.add_cascade(label="🔧 工具", menu=tools_menu)
        tools_menu.add_command(label="📊 统计报告", command=self.show_statistics_dialog)
        tools_menu.add_command(label="🎨 主题设置", command=self.show_theme_settings)
        tools_menu.add_command(label="📈 性能监控", command=self.show_performance_monitor)

        # 视图菜单
        view_menu = tk.Menu(menubar, tearoff=0, bg=ModernColors.CARD_BG, fg=ModernColors.TEXT_PRIMARY)
        menubar.add_cascade(label="👁 视图", menu=view_menu)
        view_menu.add_command(label="🔍 放大", command=self.zoom_in)
        view_menu.add_command(label="🔍 缩小", command=self.zoom_out)
        view_menu.add_command(label="🔄 重置缩放", command=self.reset_zoom)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0, bg=ModernColors.CARD_BG, fg=ModernColors.TEXT_PRIMARY)
        menubar.add_cascade(label="❓ 帮助", menu=help_menu)
        help_menu.add_command(label="📖 在线文档", command=self.open_online_docs)
        help_menu.add_command(label="🐛 报告问题", command=self.report_issue)
        help_menu.add_command(label="ℹ 关于", command=self.show_about_dialog)

    def create_toolbar(self):
        """创建现代化工具栏"""
        # 工具栏卡片
        toolbar_card = ModernCard(self.main_frame, padding=15)
        toolbar_card.pack(fill=tk.X, pady=(0, 10))

        toolbar_frame = toolbar_card.get_content_frame()

        # 左侧控制按钮
        left_controls = tk.Frame(toolbar_frame, bg=ModernColors.CARD_BG)
        left_controls.pack(side=tk.LEFT)

        # 开始按钮
        self.start_button = ModernButton(left_controls, "🚀 启动自动化",
                                       command=self.start_automation, style="primary")
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))

        # 停止按钮
        self.stop_button = ModernButton(left_controls, "⏹ 停止",
                                      command=self.stop_automation, style="danger")
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        self.stop_button.config(state=tk.DISABLED)

        # 暂停按钮
        self.pause_button = ModernButton(left_controls, "⏸ 暂停",
                                       command=self.pause_automation, style="secondary")
        self.pause_button.pack(side=tk.LEFT, padx=(0, 15))
        self.pause_button.config(state=tk.DISABLED)

        # 分隔线
        separator = tk.Frame(left_controls, bg=ModernColors.BORDER_LIGHT, width=2, height=40)
        separator.pack(side=tk.LEFT, padx=(0, 15))

        # 功能按钮
        function_controls = tk.Frame(toolbar_frame, bg=ModernColors.CARD_BG)
        function_controls.pack(side=tk.LEFT)

        # 统计按钮
        stats_button = ModernButton(function_controls, "📊 统计",
                                  command=self.show_statistics_dialog, style="secondary")
        stats_button.pack(side=tk.LEFT, padx=(0, 10))

        # 配置按钮
        config_button = ModernButton(function_controls, "⚙ 配置",
                                   command=self.open_config_dialog, style="secondary")
        config_button.pack(side=tk.LEFT, padx=(0, 10))

        # 右侧状态指示器
        right_status = tk.Frame(toolbar_frame, bg=ModernColors.CARD_BG)
        right_status.pack(side=tk.RIGHT)

        # 状态指示器
        self.create_status_indicator(right_status)

    def create_status_indicator(self, parent):
        """创建状态指示器"""
        status_frame = tk.Frame(parent, bg=ModernColors.CARD_BG)
        status_frame.pack(side=tk.RIGHT)

        # 状态点
        self.status_canvas = tk.Canvas(status_frame, width=20, height=20,
                                     bg=ModernColors.CARD_BG, highlightthickness=0)
        self.status_canvas.pack(side=tk.LEFT, padx=(0, 10))

        # 绘制状态点
        self.draw_status_point("ready")

        # 状态文本
        self.status_label = tk.Label(status_frame, text="系统就绪",
                                   bg=ModernColors.CARD_BG, fg=ModernColors.SUCCESS_GREEN,
                                   font=(ModernFonts.PRIMARY_FONT, ModernFonts.NORMAL_SIZE, "bold"))
        self.status_label.pack(side=tk.LEFT)

        # 启动状态动画
        self.animate_status_indicator()

    def draw_status_point(self, status="ready"):
        """绘制状态指示点"""
        self.status_canvas.delete("all")

        colors = {
            "ready": ModernColors.SUCCESS_GREEN,
            "running": ModernColors.PRIMARY_BLUE,
            "paused": ModernColors.WARNING_ORANGE,
            "error": ModernColors.ERROR_RED,
            "stopped": ModernColors.TEXT_MUTED
        }

        color = colors.get(status, ModernColors.SUCCESS_GREEN)

        # 绘制主圆点
        self.status_canvas.create_oval(4, 4, 16, 16, fill=color, outline=color, width=2)

        # 添加发光效果
        if status == "running":
            self.status_canvas.create_oval(2, 2, 18, 18, outline=color, width=1)
            self.status_canvas.create_oval(0, 0, 20, 20, outline=color, width=1)

    def animate_status_indicator(self):
        """动画状态指示器"""
        if self.is_running:
            self.draw_status_point("running")
            self.status_label.config(text="运行中...", fg=ModernColors.PRIMARY_BLUE)
        else:
            self.draw_status_point("ready")
            self.status_label.config(text="系统就绪", fg=ModernColors.SUCCESS_GREEN)

        # 继续动画
        self.root.after(1000, self.animate_status_indicator)

    def create_content_area(self):
        """创建主要内容区域"""
        # 创建标签页控件
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建各个标签页
        self.create_control_tab()
        self.create_progress_tab()
        self.create_logs_tab()
        self.create_config_tab()

    def create_status_bar(self):
        """创建状态栏"""
        status_card = ModernCard(self.main_frame, padding=10)
        status_card.pack(fill=tk.X)

        status_frame = status_card.get_content_frame()

        # 左侧信息
        left_info = tk.Frame(status_frame, bg=ModernColors.CARD_BG)
        left_info.pack(side=tk.LEFT)

        self.status_info_label = tk.Label(left_info, text="就绪",
                                        bg=ModernColors.CARD_BG, fg=ModernColors.TEXT_SECONDARY,
                                        font=(ModernFonts.PRIMARY_FONT, ModernFonts.SMALL_SIZE))
        self.status_info_label.pack(side=tk.LEFT)

        # 右侧时间
        right_info = tk.Frame(status_frame, bg=ModernColors.CARD_BG)
        right_info.pack(side=tk.RIGHT)

        self.time_label = tk.Label(right_info, text="",
                                 bg=ModernColors.CARD_BG, fg=ModernColors.TEXT_MUTED,
                                 font=(ModernFonts.PRIMARY_FONT, ModernFonts.SMALL_SIZE))
        self.time_label.pack(side=tk.RIGHT)

        # 更新时间显示
        self.update_time_display()

    def update_time_display(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time_display)

    # ==================== 标签页创建方法 ====================

    def create_control_tab(self):
        """创建控制标签页"""
        control_frame = tk.Frame(self.notebook, bg=ModernColors.DARK_BG)
        self.notebook.add(control_frame, text="🎮 控制面板")

        # 创建控制面板内容
        control_card = ModernCard(control_frame, "🚀 执行控制中心", padding=20)
        control_card.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        content_frame = control_card.get_content_frame()

        # 文件选择区域
        file_frame = tk.Frame(content_frame, bg=ModernColors.CARD_BG)
        file_frame.pack(fill=tk.X, pady=(0, 20))

        tk.Label(file_frame, text="📊 数据源文件:",
                bg=ModernColors.CARD_BG, fg=ModernColors.TEXT_ACCENT,
                font=(ModernFonts.PRIMARY_FONT, ModernFonts.LARGE_SIZE, "bold")).pack(anchor=tk.W, pady=(0, 10))

        file_input_frame = tk.Frame(file_frame, bg=ModernColors.CARD_BG)
        file_input_frame.pack(fill=tk.X)

        self.excel_path_var = tk.StringVar(value="添加好友名单.xlsx")
        self.excel_entry = tk.Entry(file_input_frame, textvariable=self.excel_path_var,
                                  bg=ModernColors.HOVER_BG, fg=ModernColors.TEXT_PRIMARY,
                                  font=(ModernFonts.PRIMARY_FONT, ModernFonts.NORMAL_SIZE),
                                  relief="flat", bd=5)
        self.excel_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        browse_button = ModernButton(file_input_frame, "📁 浏览",
                                   command=self.browse_excel_file, style="secondary")
        browse_button.pack(side=tk.RIGHT)

        # 参数配置区域
        params_frame = tk.Frame(content_frame, bg=ModernColors.CARD_BG)
        params_frame.pack(fill=tk.X, pady=(20, 0))

        tk.Label(params_frame, text="⚙ 基础参数配置:",
                bg=ModernColors.CARD_BG, fg=ModernColors.TEXT_ACCENT,
                font=(ModernFonts.PRIMARY_FONT, ModernFonts.LARGE_SIZE, "bold")).pack(anchor=tk.W, pady=(0, 15))

        # 批次大小
        batch_frame = tk.Frame(params_frame, bg=ModernColors.CARD_BG)
        batch_frame.pack(fill=tk.X, pady=(0, 10))

        tk.Label(batch_frame, text="📦 批次大小:",
                bg=ModernColors.CARD_BG, fg=ModernColors.TEXT_SECONDARY,
                font=(ModernFonts.PRIMARY_FONT, ModernFonts.NORMAL_SIZE)).pack(side=tk.LEFT)

        self.batch_size_var = tk.StringVar(value="10")
        batch_entry = tk.Entry(batch_frame, textvariable=self.batch_size_var, width=10,
                             bg=ModernColors.HOVER_BG, fg=ModernColors.TEXT_PRIMARY,
                             font=(ModernFonts.PRIMARY_FONT, ModernFonts.NORMAL_SIZE),
                             relief="flat", bd=3, justify='center')
        batch_entry.pack(side=tk.RIGHT)

        # 延迟设置
        delay_frame = tk.Frame(params_frame, bg=ModernColors.CARD_BG)
        delay_frame.pack(fill=tk.X, pady=(0, 0))

        tk.Label(delay_frame, text="⏱ 操作延迟(秒):",
                bg=ModernColors.CARD_BG, fg=ModernColors.TEXT_SECONDARY,
                font=(ModernFonts.PRIMARY_FONT, ModernFonts.NORMAL_SIZE)).pack(side=tk.LEFT)

        self.delay_var = tk.StringVar(value="2.0")
        delay_entry = tk.Entry(delay_frame, textvariable=self.delay_var, width=10,
                             bg=ModernColors.HOVER_BG, fg=ModernColors.TEXT_PRIMARY,
                             font=(ModernFonts.PRIMARY_FONT, ModernFonts.NORMAL_SIZE),
                             relief="flat", bd=3, justify='center')
        delay_entry.pack(side=tk.RIGHT)

    def create_progress_tab(self):
        """创建进度标签页"""
        progress_frame = tk.Frame(self.notebook, bg=ModernColors.DARK_BG)
        self.notebook.add(progress_frame, text="📊 进度监控")

        # 创建进度监控内容
        progress_card = ModernCard(progress_frame, "📈 实时进度监控", padding=20)
        progress_card.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        content_frame = progress_card.get_content_frame()

        # 总体进度
        overall_frame = tk.Frame(content_frame, bg=ModernColors.CARD_BG)
        overall_frame.pack(fill=tk.X, pady=(0, 20))

        tk.Label(overall_frame, text="🎯 总体进度:",
                bg=ModernColors.CARD_BG, fg=ModernColors.TEXT_ACCENT,
                font=(ModernFonts.PRIMARY_FONT, ModernFonts.LARGE_SIZE, "bold")).pack(anchor=tk.W, pady=(0, 10))

        # 进度条
        self.overall_progress = ModernProgressBar(overall_frame, width=400, height=25)

        # 进度文本
        self.progress_text = tk.Label(overall_frame, text="0/0 (0%)",
                                    bg=ModernColors.CARD_BG, fg=ModernColors.TEXT_PRIMARY,
                                    font=(ModernFonts.PRIMARY_FONT, ModernFonts.NORMAL_SIZE, "bold"))
        self.progress_text.pack(pady=(10, 0))

        # 统计信息
        stats_frame = tk.Frame(content_frame, bg=ModernColors.CARD_BG)
        stats_frame.pack(fill=tk.X, pady=(20, 0))

        tk.Label(stats_frame, text="📊 执行统计:",
                bg=ModernColors.CARD_BG, fg=ModernColors.TEXT_ACCENT,
                font=(ModernFonts.PRIMARY_FONT, ModernFonts.LARGE_SIZE, "bold")).pack(anchor=tk.W, pady=(0, 15))

        # 统计网格
        stats_grid = tk.Frame(stats_frame, bg=ModernColors.CARD_BG)
        stats_grid.pack(fill=tk.X)

        # 成功数量
        success_frame = tk.Frame(stats_grid, bg=ModernColors.CARD_BG)
        success_frame.pack(fill=tk.X, pady=(0, 8))

        tk.Label(success_frame, text="✅ 成功添加:",
                bg=ModernColors.CARD_BG, fg=ModernColors.SUCCESS_GREEN,
                font=(ModernFonts.PRIMARY_FONT, ModernFonts.NORMAL_SIZE)).pack(side=tk.LEFT)

        self.success_count_label = tk.Label(success_frame, text="0",
                                          bg=ModernColors.CARD_BG, fg=ModernColors.SUCCESS_GREEN,
                                          font=(ModernFonts.PRIMARY_FONT, ModernFonts.NORMAL_SIZE, "bold"))
        self.success_count_label.pack(side=tk.RIGHT)

        # 失败数量
        error_frame = tk.Frame(stats_grid, bg=ModernColors.CARD_BG)
        error_frame.pack(fill=tk.X, pady=(0, 8))

        tk.Label(error_frame, text="❌ 失败数量:",
                bg=ModernColors.CARD_BG, fg=ModernColors.ERROR_RED,
                font=(ModernFonts.PRIMARY_FONT, ModernFonts.NORMAL_SIZE)).pack(side=tk.LEFT)

        self.error_count_label = tk.Label(error_frame, text="0",
                                        bg=ModernColors.CARD_BG, fg=ModernColors.ERROR_RED,
                                        font=(ModernFonts.PRIMARY_FONT, ModernFonts.NORMAL_SIZE, "bold"))
        self.error_count_label.pack(side=tk.RIGHT)

    def create_logs_tab(self):
        """创建日志标签页"""
        logs_frame = tk.Frame(self.notebook, bg=ModernColors.DARK_BG)
        self.notebook.add(logs_frame, text="📝 日志查看")

        # 创建日志查看内容
        logs_card = ModernCard(logs_frame, "📋 系统日志", padding=20)
        logs_card.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        content_frame = logs_card.get_content_frame()

        # 日志文本区域
        self.log_text = scrolledtext.ScrolledText(
            content_frame,
            bg=ModernColors.DARKER_BG,
            fg=ModernColors.TEXT_PRIMARY,
            font=(ModernFonts.MONO_FONT, ModernFonts.SMALL_SIZE),
            relief="flat",
            bd=0,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # 配置日志颜色标签
        self.log_text.tag_configure("INFO", foreground=ModernColors.INFO_CYAN)
        self.log_text.tag_configure("SUCCESS", foreground=ModernColors.SUCCESS_GREEN)
        self.log_text.tag_configure("WARNING", foreground=ModernColors.WARNING_ORANGE)
        self.log_text.tag_configure("ERROR", foreground=ModernColors.ERROR_RED)

    def create_config_tab(self):
        """创建配置标签页"""
        config_frame = tk.Frame(self.notebook, bg=ModernColors.DARK_BG)
        self.notebook.add(config_frame, text="⚙ 配置管理")

        # 创建配置管理内容
        config_card = ModernCard(config_frame, "🔧 系统配置", padding=20)
        config_card.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        content_frame = config_card.get_content_frame()

        # 配置说明
        tk.Label(content_frame, text="配置功能正在开发中...",
                bg=ModernColors.CARD_BG, fg=ModernColors.TEXT_MUTED,
                font=(ModernFonts.PRIMARY_FONT, ModernFonts.LARGE_SIZE)).pack(expand=True)

    # ==================== 基本功能方法 ====================

    def start_automation(self):
        """启动自动化"""
        if not self.is_running:
            self.is_running = True
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.pause_button.config(state=tk.NORMAL)

            self.log_message("INFO", "🚀 自动化程序启动")
            self.status_info_label.config(text="运行中...")

            # 模拟进度更新
            self.simulate_progress()

    def stop_automation(self):
        """停止自动化"""
        if self.is_running:
            self.is_running = False
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            self.pause_button.config(state=tk.DISABLED)

            self.log_message("INFO", "⏹ 自动化程序停止")
            self.status_info_label.config(text="已停止")

    def pause_automation(self):
        """暂停自动化"""
        self.log_message("INFO", "⏸ 自动化程序暂停")
        self.status_info_label.config(text="已暂停")

    def browse_excel_file(self):
        """浏览Excel文件"""
        filename = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )
        if filename:
            self.excel_path_var.set(filename)
            self.log_message("INFO", f"📁 选择文件: {filename}")

    def simulate_progress(self):
        """模拟进度更新"""
        if not self.is_running:
            return

        # 模拟进度增加
        current_progress = self.overall_progress.progress
        if current_progress < 100:
            new_progress = min(100, current_progress + 2)
            self.overall_progress.set_progress(new_progress)
            self.progress_text.config(text=f"{new_progress}/100 ({new_progress}%)")

            # 更新统计
            success_count = int(new_progress * 0.8)
            error_count = int(new_progress * 0.2)
            self.success_count_label.config(text=str(success_count))
            self.error_count_label.config(text=str(error_count))

            # 继续模拟
            self.root.after(500, self.simulate_progress)
        else:
            # 完成
            self.stop_automation()
            self.log_message("SUCCESS", "✅ 自动化执行完成")

    def log_message(self, level, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_entry, level)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

    def show_statistics_dialog(self):
        """显示统计对话框"""
        messagebox.showinfo("统计信息", "统计功能正在开发中...")

    def open_config_dialog(self):
        """打开配置对话框"""
        messagebox.showinfo("配置", "配置功能正在开发中...")

    def setup_logging(self):
        """设置日志"""
        self.log_message("INFO", "🎯 系统初始化完成")
        self.log_message("INFO", "📋 等待用户操作...")

    def process_messages(self):
        """处理消息队列"""
        try:
            while True:
                message = self.message_queue.get_nowait()
                # 处理消息
                self.log_message("INFO", f"📨 收到消息: {message}")
        except queue.Empty:
            pass

        # 继续处理
        self.root.after(100, self.process_messages)

    def load_configuration(self):
        """加载配置"""
        self.log_message("INFO", "📖 加载配置文件...")

    def load_runtime_params_from_config(self):
        """从配置加载运行时参数"""
        pass

    def save_configuration(self):
        """保存配置"""
        self.log_message("INFO", "💾 保存配置文件...")
        messagebox.showinfo("保存", "配置已保存")

    def reload_config(self):
        """重新加载配置"""
        self.log_message("INFO", "🔄 重新加载配置...")
        messagebox.showinfo("重载", "配置已重新加载")

    # ==================== 菜单功能方法 ====================

    def show_theme_settings(self):
        """显示主题设置"""
        messagebox.showinfo("主题", "主题设置功能正在开发中...")

    def show_performance_monitor(self):
        """显示性能监控"""
        messagebox.showinfo("性能", "性能监控功能正在开发中...")

    def zoom_in(self):
        """放大界面"""
        messagebox.showinfo("缩放", "放大功能正在开发中...")

    def zoom_out(self):
        """缩小界面"""
        messagebox.showinfo("缩放", "缩小功能正在开发中...")

    def reset_zoom(self):
        """重置缩放"""
        messagebox.showinfo("缩放", "重置缩放功能正在开发中...")

    def open_online_docs(self):
        """打开在线文档"""
        messagebox.showinfo("文档", "在线文档功能正在开发中...")

    def report_issue(self):
        """报告问题"""
        messagebox.showinfo("反馈", "问题报告功能正在开发中...")

    def show_about_dialog(self):
        """显示关于对话框"""
        about_text = """
🚀 微信自动化控制台 v2.0.0

现代化科技感版本

特性：
• 深色主题设计
• 科技感动画效果
• 实时进度监控
• 智能日志系统
• 现代化UI组件

版本：2.0.0
创建时间：2025-01-31
        """
        messagebox.showinfo("关于", about_text)

    def on_closing(self):
        """关闭程序"""
        if self.is_running:
            if messagebox.askokcancel("退出", "程序正在运行，确定要退出吗？"):
                self.stop_automation()
                self.root.destroy()
        else:
            self.root.destroy()

    def run(self):
        """运行GUI"""
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 启动主循环
        self.root.mainloop()

# ==================== 主程序入口 ====================

def main():
    """主程序入口"""
    try:
        # 创建并运行现代化GUI
        app = WeChatAutomationModernGUI()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        messagebox.showerror("错误", f"程序启动失败: {e}")

if __name__ == "__main__":
    main()
